"use client";
import Navbar from "@/components/Navbar";
import Link from "next/link";
import Image from "next/image";

export default function About() {
  const stats = [
    { number: "10M+", label: "Lines of Code Reviewed" },
    { number: "50K+", label: "Developers Served" },
    { number: "99.9%", label: "Uptime Guarantee" },
    { number: "20+", label: "Languages Supported" }
  ];

  const team = [
    {
      name: "AI Engine",
      role: "Core Technology",
      description: "Powered by Google's Gemini AI, trained on millions of code patterns to provide human-like code reviews.",
      icon: "🤖"
    },
    {
      name: "Security First",
      role: "Privacy Protection",
      description: "Zero data retention policy ensures your code remains private and secure at all times.",
      icon: "🔒"
    },
    {
      name: "Developer Experience",
      role: "User Interface",
      description: "Built by developers, for developers. Every feature designed to enhance your coding workflow.",
      icon: "👨‍💻"
    }
  ];

  const timeline = [
    {
      year: "2024",
      title: "AstroCode Launch",
      description: "Revolutionizing code reviews with AI-powered analysis and VS Code integration."
    },
    {
      year: "2024",
      title: "Multi-Language Support",
      description: "Expanded to support 20+ programming languages with specialized analysis for each."
    },
    {
      year: "2024",
      title: "Enterprise Features",
      description: "Added team collaboration, analytics, and enterprise security features."
    },
    {
      year: "Future",
      title: "What's Next",
      description: "Real-time collaboration, IDE plugins, and advanced AI pair programming features."
    }
  ];

  return (
    <main className="w-full min-h-screen bg text-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 md:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                About <span className="text-[#6E27E0]">AstroCode</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-300 mb-6">
                We're on a mission to democratize code quality by making professional-grade 
                code reviews accessible to every developer, regardless of team size or experience level.
              </p>
              <p className="text-gray-300 mb-8">
                Born from the frustration of slow, inconsistent code reviews, AstroCode combines 
                cutting-edge AI with decades of software engineering best practices to deliver 
                instant, comprehensive code analysis that helps developers write better code, faster.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/features"
                  className="px-6 py-3 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium text-center"
                >
                  Explore Features
                </Link>
                <Link
                  href="/code-review"
                  className="px-6 py-3 border border-[#6E27E0] text-[#6E27E0] rounded-lg hover:bg-[#6E27E0] hover:text-white transition-colors font-medium text-center"
                >
                  Try It Now
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-[#6E27E0] to-purple-600 rounded-2xl p-8 text-center">
                <div className="text-6xl mb-4">🚀</div>
                <h3 className="text-2xl font-bold mb-4">Our Vision</h3>
                <p className="text-lg">
                  A world where every line of code is reviewed, optimized, and secure - 
                  making software development faster, safer, and more enjoyable for everyone.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 md:px-8 bg-gray-900 bg-opacity-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
            Trusted by Developers <span className="text-[#6E27E0]">Worldwide</span>
          </h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-[#6E27E0] mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-300">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Technology */}
      <section className="py-16 px-4 md:px-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
            Powered by <span className="text-[#6E27E0]">Advanced Technology</span>
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-gray-800 bg-opacity-50 rounded-xl p-6 text-center">
                <div className="text-4xl mb-4">{member.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{member.name}</h3>
                <div className="text-[#6E27E0] font-medium mb-3">{member.role}</div>
                <p className="text-gray-300 text-sm">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Journey */}
      <section className="py-16 px-4 md:px-8 bg-gray-900 bg-opacity-50">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
            Our <span className="text-[#6E27E0]">Journey</span>
          </h2>
          <div className="space-y-8">
            {timeline.map((item, index) => (
              <div key={index} className="flex gap-6">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-[#6E27E0] rounded-full flex items-center justify-center font-bold">
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                    <h3 className="text-xl font-semibold">{item.title}</h3>
                    <span className="text-[#6E27E0] font-medium">{item.year}</span>
                  </div>
                  <p className="text-gray-300">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 px-4 md:px-8">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
            Our <span className="text-[#6E27E0]">Core Values</span>
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🎯",
                title: "Developer-First",
                description: "Every decision we make prioritizes the developer experience and workflow efficiency."
              },
              {
                icon: "🔒",
                title: "Privacy by Design",
                description: "Your code is yours. We never store, share, or analyze your intellectual property."
              },
              {
                icon: "⚡",
                title: "Speed & Quality",
                description: "Fast doesn't mean compromised. We deliver both speed and comprehensive analysis."
              },
              {
                icon: "🌍",
                title: "Accessibility",
                description: "Professional code review tools should be available to developers everywhere."
              },
              {
                icon: "🔬",
                title: "Innovation",
                description: "We continuously push the boundaries of what's possible with AI and code analysis."
              },
              {
                icon: "🤝",
                title: "Community",
                description: "We're building tools for the developer community, with input from the community."
              }
            ].map((value, index) => (
              <div key={index} className="bg-gray-800 bg-opacity-50 rounded-xl p-6 text-center">
                <div className="text-3xl mb-4">{value.icon}</div>
                <h3 className="text-lg font-semibold mb-3">{value.title}</h3>
                <p className="text-gray-300 text-sm">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 md:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Join the Code Quality Revolution
          </h2>
          <p className="text-lg text-gray-300 mb-8">
            Experience the future of code reviews today. No setup required, no credit card needed.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/code-review"
              className="px-8 py-4 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium"
            >
              Start Free Review
            </Link>
            <Link
              href="/blog"
              className="px-8 py-4 border border-[#6E27E0] text-[#6E27E0] rounded-lg hover:bg-[#6E27E0] hover:text-white transition-colors font-medium"
            >
              Read Our Blog
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
