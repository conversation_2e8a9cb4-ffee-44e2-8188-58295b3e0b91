import type { Metada<PERSON> } from "next";
import { Clerk<PERSON><PERSON>ider } from '@clerk/nextjs'
import "./globals.css";

export const metadata: Metadata = {
  title: "AstroCode",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`antialiased`}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  );
}
