"use client";
import Navbar from "@/components/Navbar";
import Link from "next/link";
import { useState } from "react";

export default function Blog() {
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Posts", count: 12 },
    { id: "ai", name: "AI & Code Review", count: 4 },
    { id: "best-practices", name: "Best Practices", count: 3 },
    { id: "tutorials", name: "Tutorials", count: 3 },
    { id: "industry", name: "Industry Insights", count: 2 }
  ];

  const blogPosts = [
    {
      id: 1,
      title: "The Future of Code Reviews: How AI is Transforming Software Development",
      excerpt: "Discover how AI-powered code reviews are revolutionizing the way developers write, review, and maintain code, leading to faster delivery and higher quality software.",
      category: "ai",
      readTime: "8 min read",
      date: "Dec 15, 2024",
      featured: true,
      tags: ["AI", "Code Review", "Future Tech"]
    },
    {
      id: 2,
      title: "10 TypeScript Best Practices Every Developer Should Know",
      excerpt: "Master TypeScript with these essential best practices that will make your code more maintainable, type-safe, and performant.",
      category: "best-practices",
      readTime: "12 min read",
      date: "Dec 12, 2024",
      featured: true,
      tags: ["TypeScript", "Best Practices", "JavaScript"]
    },
    {
      id: 3,
      title: "Building Secure React Applications: A Complete Guide",
      excerpt: "Learn how to identify and prevent common security vulnerabilities in React applications with practical examples and actionable tips.",
      category: "tutorials",
      readTime: "15 min read",
      date: "Dec 10, 2024",
      featured: false,
      tags: ["React", "Security", "Frontend"]
    },
    {
      id: 4,
      title: "Why Manual Code Reviews Are Becoming Obsolete",
      excerpt: "Explore the limitations of traditional code reviews and how automated AI analysis is providing faster, more consistent, and comprehensive feedback.",
      category: "industry",
      readTime: "6 min read",
      date: "Dec 8, 2024",
      featured: false,
      tags: ["Industry", "Automation", "Productivity"]
    },
    {
      id: 5,
      title: "Getting Started with AstroCode: Your First AI Code Review",
      excerpt: "A step-by-step tutorial on how to use AstroCode to review your first piece of code and understand the AI-generated feedback.",
      category: "tutorials",
      readTime: "10 min read",
      date: "Dec 5, 2024",
      featured: false,
      tags: ["Tutorial", "Getting Started", "AstroCode"]
    },
    {
      id: 6,
      title: "The Psychology of Code Quality: Why Clean Code Matters",
      excerpt: "Understand the psychological and business impact of code quality on developer productivity, team morale, and project success.",
      category: "best-practices",
      readTime: "9 min read",
      date: "Dec 3, 2024",
      featured: false,
      tags: ["Code Quality", "Psychology", "Team Dynamics"]
    },
    {
      id: 7,
      title: "AI vs Human Code Reviews: A Comprehensive Comparison",
      excerpt: "An in-depth analysis comparing AI-powered code reviews with traditional human reviews, covering speed, accuracy, and cost-effectiveness.",
      category: "ai",
      readTime: "14 min read",
      date: "Nov 30, 2024",
      featured: false,
      tags: ["AI", "Comparison", "Analysis"]
    },
    {
      id: 8,
      title: "Common Python Pitfalls and How to Avoid Them",
      excerpt: "Identify and prevent the most common Python programming mistakes that can lead to bugs, performance issues, and security vulnerabilities.",
      category: "best-practices",
      readTime: "11 min read",
      date: "Nov 28, 2024",
      featured: false,
      tags: ["Python", "Common Mistakes", "Debugging"]
    }
  ];

  const filteredPosts = selectedCategory === "all" 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  const featuredPosts = blogPosts.filter(post => post.featured);

  return (
    <main className="w-full min-h-screen bg text-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 md:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            AstroCode <span className="text-[#6E27E0]">Blog</span>
          </h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Insights, tutorials, and best practices for modern software development. 
            Learn how to write better code, leverage AI tools, and stay ahead in the rapidly evolving tech landscape.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/code-review"
              className="px-6 py-3 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium"
            >
              Try AstroCode
            </Link>
            <Link
              href="#latest"
              className="px-6 py-3 border border-[#6E27E0] text-[#6E27E0] rounded-lg hover:bg-[#6E27E0] hover:text-white transition-colors font-medium"
            >
              Read Latest Posts
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-16 px-4 md:px-8 bg-gray-900 bg-opacity-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold mb-8">Featured Articles</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {featuredPosts.map((post) => (
              <article key={post.id} className="bg-gray-800 bg-opacity-50 rounded-xl overflow-hidden hover:bg-opacity-70 transition-all duration-300">
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <span className="px-3 py-1 bg-[#6E27E0] bg-opacity-20 text-[#6E27E0] rounded-full text-sm font-medium">
                      Featured
                    </span>
                    <span className="text-gray-400 text-sm">{post.date}</span>
                    <span className="text-gray-400 text-sm">{post.readTime}</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 hover:text-[#6E27E0] transition-colors">
                    <Link href={`/blog/${post.id}`}>{post.title}</Link>
                  </h3>
                  <p className="text-gray-300 mb-4">{post.excerpt}</p>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Categories & Posts */}
      <section id="latest" className="py-16 px-4 md:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <aside className="lg:w-1/4">
              <div className="bg-gray-800 bg-opacity-50 rounded-xl p-6 sticky top-8">
                <h3 className="text-lg font-semibold mb-4">Categories</h3>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === category.id
                          ? "bg-[#6E27E0] text-white"
                          : "hover:bg-gray-700 text-gray-300"
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{category.name}</span>
                        <span className="text-sm opacity-75">({category.count})</span>
                      </div>
                    </button>
                  ))}
                </div>
                
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-4">Newsletter</h3>
                  <p className="text-gray-300 text-sm mb-4">
                    Get the latest insights on AI, code quality, and development best practices.
                  </p>
                  <div className="space-y-3">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#6E27E0]"
                    />
                    <button className="w-full px-4 py-2 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium">
                      Subscribe
                    </button>
                  </div>
                </div>
              </div>
            </aside>

            {/* Main Content */}
            <main className="lg:w-3/4">
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-2xl font-bold">
                  {selectedCategory === "all" ? "All Posts" : categories.find(c => c.id === selectedCategory)?.name}
                </h2>
                <span className="text-gray-400">
                  {filteredPosts.length} article{filteredPosts.length !== 1 ? 's' : ''}
                </span>
              </div>

              <div className="space-y-6">
                {filteredPosts.map((post) => (
                  <article key={post.id} className="bg-gray-800 bg-opacity-50 rounded-xl p-6 hover:bg-opacity-70 transition-all duration-300">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-4">
                      <span className="text-gray-400 text-sm">{post.date}</span>
                      <span className="text-gray-400 text-sm">{post.readTime}</span>
                      <span className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-xs">
                        {categories.find(c => c.id === post.category)?.name}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold mb-3 hover:text-[#6E27E0] transition-colors">
                      <Link href={`/blog/${post.id}`}>{post.title}</Link>
                    </h3>
                    <p className="text-gray-300 mb-4">{post.excerpt}</p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                    <Link
                      href={`/blog/${post.id}`}
                      className="text-[#6E27E0] hover:text-purple-300 transition-colors font-medium"
                    >
                      Read More →
                    </Link>
                  </article>
                ))}
              </div>
            </main>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 md:px-8 bg-gray-900 bg-opacity-50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Experience AI-Powered Code Reviews?
          </h2>
          <p className="text-lg text-gray-300 mb-8">
            Put our insights into practice. Try AstroCode and see how AI can transform your development workflow.
          </p>
          <Link
            href="/code-review"
            className="inline-block px-8 py-4 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium text-lg"
          >
            Start Your First Review →
          </Link>
        </div>
      </section>
    </main>
  );
}
