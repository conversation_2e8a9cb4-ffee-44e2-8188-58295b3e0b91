"use client";
import Navbar from "@/components/Navbar";
import Link from "next/link";
import { useState } from "react";

export default function Features() {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      icon: "🤖",
      title: "AI-Powered Code Analysis",
      description: "Advanced AI reviews your code like a senior developer, catching bugs and suggesting improvements instantly.",
      benefits: ["Detects 95% more issues than traditional linters", "Learns from millions of code patterns", "Provides context-aware suggestions"],
      demo: "Analyzes TypeScript, JavaScript, Python, and more with deep understanding of language-specific best practices."
    },
    {
      icon: "⚡",
      title: "Lightning-Fast Reviews",
      description: "Get comprehensive code reviews in seconds, not hours. No more waiting for human reviewers.",
      benefits: ["Sub-10 second analysis", "Real-time feedback as you type", "Instant deployment readiness checks"],
      demo: "Upload 1000+ lines of code and get detailed feedback faster than you can read it."
    },
    {
      icon: "🎯",
      title: "VS Code Experience",
      description: "Professional Monaco editor with all VS Code features - syntax highlighting, IntelliSense, and more.",
      benefits: ["Familiar VS Code interface", "Multi-cursor editing", "Advanced find & replace", "Code folding & minimap"],
      demo: "Experience the exact same editor that powers Visual Studio Code, right in your browser."
    },
    {
      icon: "🔒",
      title: "Enterprise Security",
      description: "Your code never leaves your control. Local processing with optional cloud AI for enhanced analysis.",
      benefits: ["Zero data retention", "End-to-end encryption", "GDPR & SOC2 compliant", "On-premise deployment available"],
      demo: "Review sensitive code with confidence - your intellectual property stays protected."
    },
    {
      icon: "📊",
      title: "Smart Analytics",
      description: "Track code quality trends, identify patterns, and measure improvement over time.",
      benefits: ["Quality score tracking", "Technical debt analysis", "Team performance insights", "Custom reporting"],
      demo: "Visualize your code quality journey with detailed metrics and actionable insights."
    },
    {
      icon: "🌐",
      title: "Multi-Language Support",
      description: "Support for 20+ programming languages with language-specific best practices and conventions.",
      benefits: ["TypeScript/JavaScript expertise", "Python PEP compliance", "React/Vue component analysis", "Backend framework optimization"],
      demo: "From frontend frameworks to backend APIs - we speak your language."
    }
  ];

  return (
    <main className="w-full min-h-screen bg text-white">
      <Navbar />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 md:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
            Features That <span className="text-[#6E27E0]">Transform</span> Your Code
          </h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Discover how AstroCode's cutting-edge features solve real development challenges 
            and accelerate your coding workflow like never before.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/code-review"
              className="px-8 py-3 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium"
            >
              Try Features Now
            </Link>
            <Link
              href="/about"
              className="px-8 py-3 border border-[#6E27E0] text-[#6E27E0] rounded-lg hover:bg-[#6E27E0] hover:text-white transition-colors font-medium"
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>

      {/* Interactive Features Grid */}
      <section className="py-16 px-4 md:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Feature List */}
            <div className="space-y-4">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`p-6 rounded-xl cursor-pointer transition-all duration-300 ${
                    activeFeature === index
                      ? "bg-[#6E27E0] bg-opacity-20 border border-[#6E27E0]"
                      : "bg-gray-800 bg-opacity-50 hover:bg-gray-700 hover:bg-opacity-50"
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-start gap-4">
                    <span className="text-3xl">{feature.icon}</span>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                      <p className="text-gray-300 text-sm">{feature.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Feature Details */}
            <div className="bg-gray-800 bg-opacity-50 rounded-xl p-8">
              <div className="text-center mb-6">
                <span className="text-6xl">{features[activeFeature].icon}</span>
                <h3 className="text-2xl font-bold mt-4 mb-2">{features[activeFeature].title}</h3>
                <p className="text-gray-300">{features[activeFeature].demo}</p>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-[#6E27E0]">Key Benefits:</h4>
                {features[activeFeature].benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <span className="text-green-400">✓</span>
                    <span className="text-sm text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem-Solution Section */}
      <section className="py-16 px-4 md:px-8 bg-gray-900 bg-opacity-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
            Solving <span className="text-[#6E27E0]">Real Developer Problems</span>
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                problem: "😤 Slow Code Reviews",
                solution: "⚡ Instant AI Analysis",
                description: "Stop waiting days for human reviewers. Get comprehensive feedback in seconds."
              },
              {
                problem: "🐛 Hidden Bugs",
                solution: "🔍 Deep Code Scanning",
                description: "Catch subtle bugs and security vulnerabilities before they reach production."
              },
              {
                problem: "📚 Inconsistent Standards",
                solution: "📏 Automated Best Practices",
                description: "Enforce coding standards automatically across your entire team."
              },
              {
                problem: "🔒 Security Concerns",
                solution: "🛡️ Privacy-First Design",
                description: "Keep your code secure with local processing and zero data retention."
              },
              {
                problem: "🎯 Learning Curve",
                solution: "🎓 Educational Feedback",
                description: "Learn while you code with detailed explanations and improvement suggestions."
              },
              {
                problem: "⚖️ Technical Debt",
                solution: "📊 Quality Tracking",
                description: "Monitor and reduce technical debt with actionable insights and metrics."
              }
            ].map((item, index) => (
              <div key={index} className="bg-gray-800 bg-opacity-50 rounded-xl p-6 text-center">
                <div className="text-2xl mb-3">{item.problem}</div>
                <div className="text-3xl mb-3">{item.solution}</div>
                <p className="text-gray-300 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 md:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Transform Your Development Workflow?
          </h2>
          <p className="text-lg text-gray-300 mb-8">
            Join thousands of developers who've already upgraded their code review process with AstroCode.
          </p>
          <Link
            href="/code-review"
            className="inline-block px-8 py-4 bg-[#6E27E0] text-white rounded-lg hover:bg-[#6e27e0a5] transition-colors font-medium text-lg"
          >
            Start Reviewing Code Now →
          </Link>
        </div>
      </section>
    </main>
  );
}
