{"name": "astrocode", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.12.3", "@google/generative-ai": "^0.24.0", "@tailwindcss/postcss": "^4.0.9", "axios": "^1.8.2", "gsap": "^3.12.7", "next": "15.2.0", "postcss": "^8.5.3", "prismjs": "^1.30.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-simple-code-editor": "^0.14.1", "rehype-highlight": "^7.0.2", "tailwindcss": "^4.0.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "typescript": "^5"}}