/**
 * Code Analyzer Service
 * Provides static analysis and metrics for code before AI review
 */

export interface CodeMetrics {
  lineCount: number;
  characterCount: number;
  wordCount: number;
  complexity: 'low' | 'medium' | 'high';
  estimatedReviewTime: number; // in seconds
  detectedPatterns: string[];
  potentialIssues: string[];
}

export interface LanguageInfo {
  name: string;
  extension: string;
  category: 'frontend' | 'backend' | 'styling' | 'markup' | 'other';
  supportsLinting: boolean;
}

export class CodeAnalyzerService {
  
  /**
   * Language configuration
   */
  private static readonly LANGUAGES: Record<string, LanguageInfo> = {
    typescript: {
      name: 'TypeScript',
      extension: '.ts',
      category: 'frontend',
      supportsLinting: true
    },
    javascript: {
      name: 'JavaScript',
      extension: '.js',
      category: 'frontend',
      supportsLinting: true
    },
    jsx: {
      name: 'JSX',
      extension: '.jsx',
      category: 'frontend',
      supportsLinting: true
    },
    tsx: {
      name: 'TSX',
      extension: '.tsx',
      category: 'frontend',
      supportsLinting: true
    },
    python: {
      name: 'Python',
      extension: '.py',
      category: 'backend',
      supportsLinting: true
    },
    css: {
      name: 'CSS',
      extension: '.css',
      category: 'styling',
      supportsLinting: true
    },
    html: {
      name: 'HTML',
      extension: '.html',
      category: 'markup',
      supportsLinting: true
    }
  };

  /**
   * Analyze code and return metrics
   */
  static analyzeCode(code: string, language: string): CodeMetrics {
    const lines = code.split('\n');
    const lineCount = lines.length;
    const characterCount = code.length;
    const wordCount = code.split(/\s+/).filter(word => word.length > 0).length;

    // Calculate complexity based on various factors
    const complexity = this.calculateComplexity(code, language);
    
    // Estimate review time (rough calculation)
    const estimatedReviewTime = this.estimateReviewTime(lineCount, complexity);

    // Detect patterns and potential issues
    const detectedPatterns = this.detectPatterns(code, language);
    const potentialIssues = this.detectPotentialIssues(code, language);

    return {
      lineCount,
      characterCount,
      wordCount,
      complexity,
      estimatedReviewTime,
      detectedPatterns,
      potentialIssues
    };
  }

  /**
   * Calculate code complexity
   */
  private static calculateComplexity(code: string, language: string): 'low' | 'medium' | 'high' {
    let complexityScore = 0;

    // Base complexity on line count
    const lineCount = code.split('\n').length;
    if (lineCount > 100) complexityScore += 2;
    else if (lineCount > 50) complexityScore += 1;

    // Language-specific complexity indicators
    const complexityPatterns = {
      typescript: [
        /interface\s+\w+/g,
        /type\s+\w+\s*=/g,
        /generic\s*<.*>/g,
        /async\s+function/g,
        /Promise\s*</g
      ],
      javascript: [
        /async\s+function/g,
        /\.then\s*\(/g,
        /\.catch\s*\(/g,
        /setTimeout|setInterval/g,
        /addEventListener/g
      ],
      jsx: [
        /useEffect\s*\(/g,
        /useState\s*\(/g,
        /useCallback\s*\(/g,
        /useMemo\s*\(/g,
        /React\.Component/g
      ],
      tsx: [
        /interface\s+\w+Props/g,
        /React\.FC\s*</g,
        /useEffect\s*\(/g,
        /useState\s*</g
      ],
      python: [
        /class\s+\w+/g,
        /def\s+\w+/g,
        /async\s+def/g,
        /try:\s*$/gm,
        /except\s+\w+/g
      ],
      css: [
        /@media\s*\(/g,
        /@keyframes/g,
        /::before|::after/g,
        /calc\s*\(/g
      ],
      html: [
        /<script/g,
        /<style/g,
        /data-\w+/g,
        /aria-\w+/g
      ]
    };

    const patterns = complexityPatterns[language as keyof typeof complexityPatterns] || [];
    patterns.forEach(pattern => {
      const matches = code.match(pattern);
      if (matches) {
        complexityScore += matches.length * 0.5;
      }
    });

    // Determine complexity level
    if (complexityScore >= 10) return 'high';
    if (complexityScore >= 5) return 'medium';
    return 'low';
  }

  /**
   * Estimate review time in seconds
   */
  private static estimateReviewTime(lineCount: number, complexity: 'low' | 'medium' | 'high'): number {
    const baseTime = Math.max(30, lineCount * 2); // 2 seconds per line, minimum 30 seconds
    
    const complexityMultiplier = {
      low: 1,
      medium: 1.5,
      high: 2
    };

    return Math.round(baseTime * complexityMultiplier[complexity]);
  }

  /**
   * Detect code patterns
   */
  private static detectPatterns(code: string, language: string): string[] {
    const patterns: string[] = [];

    const patternDetectors = {
      typescript: [
        { pattern: /interface\s+\w+/g, name: 'Interface definitions' },
        { pattern: /type\s+\w+\s*=/g, name: 'Type aliases' },
        { pattern: /enum\s+\w+/g, name: 'Enumerations' },
        { pattern: /async\s+function/g, name: 'Async functions' }
      ],
      javascript: [
        { pattern: /function\s+\w+/g, name: 'Function declarations' },
        { pattern: /const\s+\w+\s*=\s*\(/g, name: 'Arrow functions' },
        { pattern: /class\s+\w+/g, name: 'ES6 Classes' },
        { pattern: /import\s+.*from/g, name: 'ES6 Imports' }
      ],
      jsx: [
        { pattern: /useState\s*\(/g, name: 'React Hooks (useState)' },
        { pattern: /useEffect\s*\(/g, name: 'React Hooks (useEffect)' },
        { pattern: /<\w+.*>/g, name: 'JSX Components' }
      ],
      python: [
        { pattern: /class\s+\w+/g, name: 'Class definitions' },
        { pattern: /def\s+\w+/g, name: 'Function definitions' },
        { pattern: /import\s+\w+/g, name: 'Import statements' },
        { pattern: /if\s+__name__\s*==\s*['"']__main__['"']/g, name: 'Main guard' }
      ]
    };

    const detectors = patternDetectors[language as keyof typeof patternDetectors] || [];
    detectors.forEach(detector => {
      const matches = code.match(detector.pattern);
      if (matches && matches.length > 0) {
        patterns.push(`${detector.name} (${matches.length})`);
      }
    });

    return patterns;
  }

  /**
   * Detect potential issues
   */
  private static detectPotentialIssues(code: string, language: string): string[] {
    const issues: string[] = [];

    // Common issues across languages
    if (code.includes('console.log')) {
      issues.push('Debug console.log statements found');
    }

    if (code.includes('TODO') || code.includes('FIXME')) {
      issues.push('TODO/FIXME comments found');
    }

    // Language-specific issues
    const issueDetectors = {
      typescript: [
        { pattern: /any\s*[;,\]\)]/g, name: 'Usage of "any" type' },
        { pattern: /!\s*[;\]\)]/g, name: 'Non-null assertion operator usage' }
      ],
      javascript: [
        { pattern: /var\s+\w+/g, name: 'Usage of "var" instead of "let/const"' },
        { pattern: /==\s*[^=]/g, name: 'Usage of "==" instead of "===" }
      ],
      jsx: [
        { pattern: /dangerouslySetInnerHTML/g, name: 'Potentially unsafe innerHTML usage' }
      ],
      python: [
        { pattern: /except:\s*$/gm, name: 'Bare except clauses' },
        { pattern: /eval\s*\(/g, name: 'Usage of eval() function' }
      ]
    };

    const detectors = issueDetectors[language as keyof typeof issueDetectors] || [];
    detectors.forEach(detector => {
      const matches = code.match(detector.pattern);
      if (matches && matches.length > 0) {
        issues.push(detector.name);
      }
    });

    return issues;
  }

  /**
   * Get language information
   */
  static getLanguageInfo(language: string): LanguageInfo | null {
    return this.LANGUAGES[language.toLowerCase()] || null;
  }

  /**
   * Get all supported languages
   */
  static getSupportedLanguages(): Record<string, LanguageInfo> {
    return this.LANGUAGES;
  }

  /**
   * Validate if language is supported
   */
  static isLanguageSupported(language: string): boolean {
    return language.toLowerCase() in this.LANGUAGES;
  }
}
